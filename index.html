<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上市公司财报分析幻灯片</title>
    <!-- 引入ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入Bootstrap用于样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 移除了外部RecordRTC库，改为使用浏览器原生的MediaRecorder API -->
    <style>
        /* 基础样式 - 金融PPT风格 */
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            color: #2c3e50;
        }
        
        /* 幻灯片容器样式 - 金融PPT风格 */
        .slideshow-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            overflow: hidden;
            background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
        }
        
        /* 单个幻灯片样式 - 金融PPT风格，修复重影问题 */
        .slide {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease-in-out;
            padding: 30px 60px;
            display: flex;
            flex-direction: column;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(52, 152, 219, 0.03) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(52, 73, 94, 0.03) 0%, transparent 20%);
            /* 启用硬件加速 */
            transform: translateZ(0);
            will-change: opacity;
            /* 防止闪烁 */
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            /* 确保非活动幻灯片不可见 */
            z-index: 1;
        }

        /* 活动幻灯片样式 */
        .slide.active {
            opacity: 1;
            visibility: visible;
            z-index: 10;
        }

        /* 正在切换的幻灯片样式 */
        .slide.transitioning {
            z-index: 5;
        }
        
        /* 标题样式 - 金融PPT风格 */
        .slide-title {
            text-align: center;
            font-size: 2.8rem;
            margin-bottom: 40px;
            color: #2c3e50;
            font-weight: 600;
            letter-spacing: -0.5px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            border-bottom: 3px solid #3498db;
            padding-bottom: 20px;
        }
        
        /* 图表容器样式 - 金融PPT风格 */
        .chart-container {
            flex: 1;
            margin: 10px 0;
            background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            padding: 30px;
            border: 1px solid #e9ecef;
        }
        
        /* 图表样式 */
        .chart {
            width: 100%;
            height: 100%;
            min-height: 450px;
        }
        
        /* 幻灯片导航按钮 - 增强金融PPT风格 */
        .nav-buttons {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            display: flex;
            gap: 16px;
            padding: 12px 20px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .nav-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #bdc3c7;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background-color: #95a5a6;
            transform: scale(1.1);
        }
        
        .nav-btn.active {
            background-color: #3498db;
            border-color: #2980b9;
            transform: scale(1.3);
        }
        
        /* 左右切换按钮 - 金融PPT风格 */
        .arrow-btn {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.95);
            border: 1px solid #bdc3c7;
            cursor: pointer;
            z-index: 10;
            font-size: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        
        .arrow-btn:hover {
            background-color: rgba(255,255,255,1);
            border-color: #3498db;
            box-shadow: 0 4px 15px rgba(0,0,0,0.12);
        }
        
        .prev-btn {
            left: 30px;
        }
        
        .next-btn {
            right: 30px;
        }
        
        /* 数据卡片样式 - 金融PPT风格 */
        .data-cards {
            display: flex;
            gap: 25px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .data-card {
            flex: 1;
            min-width: 160px;
            background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
            padding: 25px;
            border-radius: 6px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            text-align: center;
            border: 1px solid #e9ecef;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .data-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }
        
        .data-value {
            font-size: 2.2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }
        
        .data-label {
            font-size: 1rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* 自动播放控制样式 - 金融PPT风格 */
        .autoplay-controls {
            position: fixed;
            top: 25px;
            right: 25px;
            z-index: 20;
            background: rgba(255,255,255,0.95);
            padding: 15px 20px;
            border-radius: 6px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            border: 1px solid #e9ecef;
        }
        
        .control-btn {
            padding: 8px 18px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        
        .play-btn {
            background-color: #34495e;
            color: white;
        }
        
        .play-btn:hover {
            background-color: #2c3e50;
        }
        
        .pause-btn {
            background-color: #7f8c8d;
            color: white;
        }
        
        .pause-btn:hover {
            background-color: #6c757d;
        }
        
        .speed-control {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .speed-control label {
            font-size: 14px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .speed-control select {
            padding: 5px 10px;
            border-radius: 4px;
            border: 1px solid #bdc3c7;
            background-color: white;
            font-size: 14px;
        }
        
        /* 视频录制按钮 - 金融PPT风格 */
        .record-btn {
            position: fixed;
            top: 25px;
            left: 25px;
            z-index: 20;
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            box-shadow: 0 3px 10px rgba(231, 76, 60, 0.3);
        }
        
        .record-btn:hover {
            background-color: #c0392b;
        }
        
        .recording {
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
            }
        }
        
        /* 页脚信息 - 增强金融PPT风格 */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 15px 30px;
            font-size: 12px;
            color: #7f8c8d;
            background-color: rgba(255, 255, 255, 0.8);
            border-top: 1px solid #e9ecef;
            z-index: 5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .footer-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .footer-right {
            text-align: right;
        }
        
        .confidential-tag {
            font-weight: 600;
            color: #e74c3c;
        }
        
        /* 水印效果 - 增强金融PPT风格 */
        .watermark-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
            opacity: 0.03;
            overflow: hidden;
        }
        
        .watermark {
            position: absolute;
            font-size: 150px;
            font-weight: 800;
            color: #34495e;
            transform-origin: center;
            z-index: 0;
        }
        
        .watermark-1 {
            top: 10%;
            left: 5%;
            transform: rotate(-15deg);
        }
        
        .watermark-2 {
            top: 50%;
            right: 5%;
            transform: rotate(15deg) translateY(-50%);
        }
        
        /* 幻灯片标题装饰 - 金融PPT风格 */
        .slide-title::after {
            content: '';
            display: block;
            width: 100px;
            height: 4px;
            background: linear-gradient(to right, #3498db, #34495e);
            margin: 15px auto 0;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <!-- 水印元素容器 - 增强金融PPT风格 -->
    <div class="watermark-container">
        <div class="watermark watermark-1">FINANCIAL</div>
        <div class="watermark watermark-2">REPORT</div>
    </div>
    
    <!-- 页脚信息 - 增强金融PPT风格 -->
    <!-- <div class="footer">
        <div class="footer-left">
            <span class="confidential-tag">CONFIDENTIAL</span>
            <span>财报分析报告</span>
        </div>
        <div class="footer-right">
            <div>2023年度上市公司财报分析</div>
            <div>© 2024 财务分析部门</div>
        </div>
    </div> -->
    
    <div class="slideshow-container">
        <!-- 封面幻灯片 -->
        <div class="slide active" style="justify-content: center; align-items: center; background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white;">
            <div style="text-align: center; padding: 50px;">
                <h1 style="font-size: 3.5rem; margin-bottom: 30px; color: white; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                    上市公司财报分析
                </h1>
                <div style="font-size: 1.5rem; margin-bottom: 50px; opacity: 0.9;">
                    2023年度
                </div>
                <!-- <div style="font-size: 1.2rem; margin-bottom: 20px; opacity: 0.8;">
                    财务分析部门
                </div>
                <div style="font-size: 1rem; opacity: 0.7;">
                    © 2024 版权所有
                </div> -->
            </div>
        </div>

        <!-- 第1张幻灯片：公司概览 -->
        <div class="slide">
            <h1 class="slide-title">上市公司财报分析 - 公司概览</h1>
            <div class="data-cards">
                <div class="data-card">
                    <div class="data-value">¥128.5亿</div>
                    <div class="data-label">年度营收</div>
                </div>
                <div class="data-card">
                    <div class="data-value">¥15.2亿</div>
                    <div class="data-label">净利润</div>
                </div>
                <div class="data-card">
                    <div class="data-value">8.7%</div>
                    <div class="data-label">同比增长</div>
                </div>
                <div class="data-card">
                    <div class="data-value">12.3%</div>
                    <div class="data-label">利润率</div>
                </div>
            </div>
            <div class="chart-container">
                <div id="overviewChart" class="chart"></div>
            </div>
        </div>

        <!-- 第2张幻灯片：季度营收分析 -->
        <div class="slide">
            <h1 class="slide-title">季度营收分析</h1>
            <div class="chart-container">
                <div id="quarterlyRevenueChart" class="chart"></div>
            </div>
        </div>

        <!-- 第3张幻灯片：业务板块分析 -->
        <div class="slide">
            <h1 class="slide-title">业务板块分析</h1>
            <div class="chart-container">
                <div id="businessSegmentChart" class="chart"></div>
            </div>
        </div>

        <!-- 第4张幻灯片：财务指标分析 -->
        <div class="slide">
            <h1 class="slide-title">财务指标分析</h1>
            <div class="chart-container">
                <div id="financialIndicatorsChart" class="chart"></div>
            </div>
        </div>

        <!-- 第5张幻灯片：未来趋势预测 -->
        <div class="slide">
            <h1 class="slide-title">未来趋势预测</h1>
            <div class="chart-container">
                <div id="futureTrendChart" class="chart"></div>
            </div>
        </div>
    </div>

    <!-- 导航按钮 -->
    <div class="nav-buttons">
        <button class="nav-btn active" data-index="0"></button>
        <button class="nav-btn" data-index="1"></button>
        <button class="nav-btn" data-index="2"></button>
        <button class="nav-btn" data-index="3"></button>
        <button class="nav-btn" data-index="4"></button>
        <button class="nav-btn" data-index="5"></button>
    </div>

    <!-- 左右切换按钮 -->
    <button class="arrow-btn prev-btn">‹</button>
    <button class="arrow-btn next-btn">›</button>
    
    <!-- 自动播放控制 -->
    <div class="autoplay-controls">
        <button id="playBtn" class="control-btn play-btn">开始自动播放</button>
        <div class="speed-control">
            <label for="slideDuration">每张停留:</label>
            <select id="slideDuration">
                <option value="2000">2秒</option>
                <option value="3000">3秒</option>
                <option value="5000" selected>5秒</option>
                <option value="8000">8秒</option>
                <option value="10000">10秒</option>
            </select>
        </div>
    </div>
    
    <!-- 视频录制按钮 -->
    <button id="recordBtn" class="record-btn">开始录制视频</button>
    
    <!-- 隐藏的canvas用于录制 -->
    <canvas id="recordCanvas" style="display: none;"></canvas>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化变量
            const slides = document.querySelectorAll('.slide');
            const navBtns = document.querySelectorAll('.nav-btn');
            const prevBtn = document.querySelector('.prev-btn');
            const nextBtn = document.querySelector('.next-btn');
            const playBtn = document.getElementById('playBtn');
            const slideDuration = document.getElementById('slideDuration');
            const recordBtn = document.getElementById('recordBtn');
            const recordCanvas = document.getElementById('recordCanvas');

            let currentSlide = 0;
            let autoplayInterval = null;
            let isAutoplaying = false;
            let recordRTC = null;
            let isRecording = false;

            // 页面性能优化：预加载关键资源
            window.addEventListener('load', function() {
                // 确保所有图表都能正确渲染
                setTimeout(() => {
                    initOverviewChart();
                }, 100);
            });
            
            // 切换幻灯片函数 - 修复重影问题
            function goToSlide(index) {
                console.log('goToSlide', index);
                // 如果是同一个幻灯片，不执行任何操作
                if (currentSlide === index) return;

                // 先获取当前活动的幻灯片
                const currentActiveSlide = document.querySelector('.slide.active');

                // 立即隐藏当前幻灯片，避免重影
                if (currentActiveSlide) {
                    currentActiveSlide.classList.remove('active');
                    currentActiveSlide.classList.add('transitioning');
                }

                // 更新当前幻灯片索引
                currentSlide = index;

                // 更新导航按钮状态
                navBtns.forEach((btn, i) => {
                    btn.classList.toggle('active', i === currentSlide);
                });

                // 预加载新幻灯片的图表
                initChartForSlide(currentSlide);

                // 短暂延迟后显示新幻灯片，确保旧幻灯片已完全隐藏
                setTimeout(() => {
                    // 清理所有过渡状态
                    slides.forEach(slide => {
                        slide.classList.remove('active', 'transitioning');
                    });

                    // 显示新幻灯片
                    slides[index].classList.add('active');
                }, 50);
            }
            
            // 初始化特定幻灯片的图表 - 优化版本
            function initChartForSlide(slideIndex) {
                // 使用requestAnimationFrame确保在合适的时机初始化图表
                requestAnimationFrame(() => {
                    switch(slideIndex) {
                        case 0:
                            // 封面幻灯片，无需初始化图表
                            break;
                        case 1:
                            initOverviewChart();
                            break;
                        case 2:
                            initQuarterlyRevenueChart();
                            break;
                        case 3:
                            initBusinessSegmentChart();
                            break;
                        case 4:
                            initFinancialIndicatorsChart();
                            break;
                        case 5:
                            initFutureTrendChart();
                            break;
                    }
                });
            }
        
            // 第1张幻灯片：公司概览图表 - 金融风格配色
            function initOverviewChart() {
                const chartDom = document.getElementById('overviewChart');
                let myChart = null;
                
                // 如果图表已经初始化过，则销毁重建
                if (chartDom) {
                    // 尝试获取已存在的图表实例
                    const existingChart = echarts.getInstanceByDom(chartDom);
                    if (existingChart) {
                        existingChart.dispose();
                    }
                    myChart = echarts.init(chartDom);
                    // 存储图表实例到DOM元素上，以便录制时使用
                    chartDom.__myChart = myChart;
                }
                
                // 金融PPT风格配色方案
                const financialColors = {
                    primary: '#2c3e50',
                    secondary: '#34495e',
                    accent: '#3498db',
                    highlight: '#e74c3c',
                    success: '#27ae60',
                    warning: '#f39c12',
                    background: '#f8f9fa'
                };
                
                const option = {
                    title: {
                        text: '年度财务概览',
                        left: 'center',
                        textStyle: {
                            color: financialColors.primary,
                            fontWeight: 'bold',
                            fontSize: 18
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow',
                            lineStyle: {
                                color: financialColors.secondary,
                                width: 1
                            }
                        },
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: financialColors.accent,
                        borderWidth: 1,
                        textStyle: {
                            color: financialColors.primary
                        }
                    },
                    legend: {
                        data: ['营收', '净利润'],
                        bottom: 0,
                        textStyle: {
                            color: financialColors.secondary
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '10%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['2019', '2020', '2021', '2022', '2023'],
                        axisLine: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        },
                        axisLabel: {
                            color: financialColors.secondary
                        },
                        axisTick: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '¥{value}亿',
                            color: financialColors.secondary
                        },
                        axisLine: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        },
                        axisTick: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(52, 73, 94, 0.1)'
                            }
                        }
                    },
                    series: [
                        {
                            name: '营收',
                            type: 'bar',
                            data: [92.5, 98.7, 105.2, 118.3, 128.5],
                            animationDelay: (idx) => idx * 100,
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {offset: 0, color: financialColors.accent},
                                    {offset: 1, color: financialColors.secondary}
                                ]),
                                borderRadius: [4, 4, 0, 0]
                            },
                            emphasis: {
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: financialColors.accent},
                                        {offset: 1, color: financialColors.primary}
                                    ])
                                }
                            }
                        },
                        {
                            name: '净利润',
                            type: 'line',
                            smooth: true,
                            data: [8.3, 9.5, 11.2, 13.8, 15.2],
                            animationDelay: (idx) => idx * 100 + 300,
                            lineStyle: {
                                width: 4,
                                color: financialColors.success
                            },
                            symbol: 'circle',
                            symbolSize: 10,
                            itemStyle: {
                                color: financialColors.success,
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            emphasis: {
                                symbolSize: 12
                            }
                        }
                    ],
                    animationEasing: 'elasticOut',
                    animationDelayUpdate: (idx) => idx * 5,
                    animationDuration: 3000
                };
                
                myChart.setOption(option);
                
                // 监听窗口大小变化，重新调整图表大小
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
        
            // 第2张幻灯片：季度营收分析图表 - 金融风格配色
            function initQuarterlyRevenueChart() {
                const chartDom = document.getElementById('quarterlyRevenueChart');
                let myChart = null;
                
                // 如果图表已经初始化过，则销毁重建
                if (chartDom) {
                    // 尝试获取已存在的图表实例
                    const existingChart = echarts.getInstanceByDom(chartDom);
                    if (existingChart) {
                        existingChart.dispose();
                    }
                    myChart = echarts.init(chartDom);
                    // 存储图表实例到DOM元素上，以便录制时使用
                    chartDom.__myChart = myChart;
                }
                
                // 金融PPT风格配色方案
                const financialColors = {
                    primary: '#2c3e50',
                    secondary: '#34495e',
                    accent: '#3498db',
                    highlight: '#e74c3c',
                    success: '#27ae60',
                    warning: '#f39c12',
                    background: '#f8f9fa'
                };
                
                const option = {
                    title: {
                        text: '2023年季度营收与同比增长',
                        left: 'center',
                        textStyle: {
                            color: financialColors.primary,
                            fontWeight: 'bold',
                            fontSize: 18
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: financialColors.accent,
                        borderWidth: 1,
                        textStyle: {
                            color: financialColors.primary
                        }
                    },
                    legend: {
                        data: ['季度营收', '同比增长'],
                        bottom: 0,
                        textStyle: {
                            color: financialColors.secondary
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '10%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['Q1', 'Q2', 'Q3', 'Q4'],
                        axisLine: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        },
                        axisLabel: {
                            color: financialColors.secondary
                        },
                        axisTick: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        }
                    },
                    yAxis: [
                    {
                        type: 'value',
                        name: '营收(亿元)',
                        position: 'left',
                        nameTextStyle: {
                            color: financialColors.secondary
                        },
                        axisLabel: {
                            formatter: '¥{value}',
                            color: financialColors.secondary
                        },
                        axisLine: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        },
                        axisTick: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(52, 73, 94, 0.1)'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '同比增长(%)',
                        position: 'right',
                        nameTextStyle: {
                            color: financialColors.secondary
                        },
                        axisLabel: {
                            formatter: '{value}%',
                            color: financialColors.secondary
                        },
                        axisLine: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        },
                        axisTick: {
                            lineStyle: {
                                color: financialColors.secondary
                            }
                        }
                    }
                ],
                series: [
                    {
                        name: '季度营收',
                        type: 'bar',
                        data: [28.5, 32.1, 33.8, 34.1],
                        animationDelay: (idx) => idx * 100,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: financialColors.accent},
                                {offset: 1, color: financialColors.secondary}
                            ]),
                            borderRadius: [4, 4, 0, 0]
                        },
                        emphasis: {
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {offset: 0, color: financialColors.accent},
                                    {offset: 1, color: financialColors.primary}
                                ])
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '¥{c}',
                            color: financialColors.primary,
                            fontSize: 10,
                            fontWeight: 'bold'
                        }
                    },
                    {
                        name: '同比增长',
                        type: 'line',
                        yAxisIndex: 1,
                        data: [7.2, 8.5, 9.1, 9.3],
                        smooth: true,
                        animationDelay: (idx) => idx * 100 + 300,
                        lineStyle: {
                            width: 4,
                            color: financialColors.success
                        },
                        symbol: 'circle',
                        symbolSize: 10,
                        itemStyle: {
                            color: financialColors.success,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        emphasis: {
                            symbolSize: 12
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: 'rgba(39, 174, 96, 0.3)'},
                                {offset: 1, color: 'rgba(39, 174, 96, 0.05)'}
                            ])
                        }
                    }
                ],
                animationEasing: 'elasticOut',
                animationDelayUpdate: (idx) => idx * 5,
                animationDuration: 3000
            };
            
            myChart.setOption(option);
            
            // 监听窗口大小变化，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 第3张幻灯片：业务板块分析图表 - 金融风格配色
        function initBusinessSegmentChart() {
            const chartDom = document.getElementById('businessSegmentChart');
            let myChart = null;
            
            // 如果图表已经初始化过，则销毁重建
            if (chartDom) {
                // 尝试获取已存在的图表实例
                const existingChart = echarts.getInstanceByDom(chartDom);
                if (existingChart) {
                    existingChart.dispose();
                }
                myChart = echarts.init(chartDom);
                // 存储图表实例到DOM元素上，以便录制时使用
                chartDom.__myChart = myChart;
            }
            
            // 金融PPT风格配色方案
            const financialColors = {
                primary: '#2c3e50',
                secondary: '#34495e',
                accent: '#3498db',
                highlight: '#e74c3c',
                success: '#27ae60',
                warning: '#f39c12',
                background: '#f8f9fa'
            };
            
            const option = {
                title: {
                    text: '业务板块收入占比与增长率',
                    left: 'center',
                    textStyle: {
                        color: financialColors.primary,
                        fontWeight: 'bold',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: financialColors.accent,
                    borderWidth: 1,
                    textStyle: {
                        color: financialColors.primary
                    },
                    formatter: '{a} <br/>{b}: {c}% ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    bottom: 0,
                    textStyle: {
                        color: financialColors.secondary
                    }
                },
                series: [
                    {
                        name: '业务收入占比',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '50%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2,
                            // 设置阴影效果
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.1)'
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 30,
                                fontWeight: 'bold',
                                color: financialColors.primary
                            },
                            itemStyle: {
                                shadowBlur: 20,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.3)'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            {value: 45, name: '主营业务', itemStyle: {color: financialColors.accent}},
                            {value: 25, name: '技术服务', itemStyle: {color: financialColors.success}},
                            {value: 15, name: '数字化产品', itemStyle: {color: financialColors.warning}},
                            {value: 10, name: '其他业务', itemStyle: {color: financialColors.highlight}},
                            {value: 5, name: '投资收益', itemStyle: {color: financialColors.secondary}}
                        ],
                        animationType: 'scale',
                        animationEasing: 'elasticOut',
                        animationDelay: function(idx) {
                            return Math.random() * 200;
                        },
                        animationDuration: 3000
                    }
                ]
            };
            
            myChart.setOption(option);
            
            // 监听窗口大小变化，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 第4张幻灯片：财务指标分析图表 - 金融风格配色
        function initFinancialIndicatorsChart() {
            const chartDom = document.getElementById('financialIndicatorsChart');
            let myChart = null;
            
            // 如果图表已经初始化过，则销毁重建
            if (chartDom) {
                // 尝试获取已存在的图表实例
                const existingChart = echarts.getInstanceByDom(chartDom);
                if (existingChart) {
                    existingChart.dispose();
                }
                myChart = echarts.init(chartDom);
                // 存储图表实例到DOM元素上，以便录制时使用
                chartDom.__myChart = myChart;
            }
            
            // 金融PPT风格配色方案
            const financialColors = {
                primary: '#2c3e50',
                secondary: '#34495e',
                accent: '#3498db',
                highlight: '#e74c3c',
                success: '#27ae60',
                warning: '#f39c12',
                background: '#f8f9fa'
            };
            
            const option = {
                title: {
                    text: '关键财务指标对比',
                    left: 'center',
                    textStyle: {
                        color: financialColors.primary,
                        fontWeight: 'bold',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                        lineStyle: {
                            color: financialColors.secondary,
                            width: 1
                        }
                    },
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: financialColors.accent,
                    borderWidth: 1,
                    textStyle: {
                        color: financialColors.primary
                    }
                },
                legend: {
                    data: ['2022年', '2023年'],
                    bottom: 0,
                    textStyle: {
                        color: financialColors.secondary
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '10%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}%',
                        color: financialColors.secondary
                    },
                    axisLine: {
                        lineStyle: {
                            color: financialColors.secondary
                        }
                    },
                    axisTick: {
                        lineStyle: {
                            color: financialColors.secondary
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(52, 73, 94, 0.1)'
                        }
                    }
                },
                yAxis: {
                    type: 'category',
                    data: ['毛利率', '净利率', 'ROE', '资产负债率', '研发投入占比'],
                    axisLine: {
                        lineStyle: {
                            color: financialColors.secondary
                        }
                    },
                    axisLabel: {
                        color: financialColors.secondary
                    },
                    axisTick: {
                        lineStyle: {
                            color: financialColors.secondary
                        }
                    }
                },
                series: [
                    {
                        name: '2022年',
                        type: 'bar',
                        data: [38.5, 11.7, 14.2, 58.3, 8.5],
                        animationDelay: (idx) => idx * 100,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                {offset: 0, color: financialColors.secondary},
                                {offset: 1, color: financialColors.accent}
                            ]),
                            borderRadius: [0, 4, 4, 0]
                        },
                        emphasis: {
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                    {offset: 0, color: financialColors.primary},
                                    {offset: 1, color: financialColors.accent}
                                ])
                            }
                        },
                        label: {
                            show: true,
                            position: 'right',
                            formatter: '{c}%',
                            color: financialColors.primary,
                            fontSize: 10,
                            fontWeight: 'bold'
                        }
                    },
                    {
                        name: '2023年',
                        type: 'bar',
                        data: [39.2, 12.3, 15.1, 56.8, 9.2],
                        animationDelay: (idx) => idx * 100 + 300,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                {offset: 0, color: financialColors.secondary},
                                {offset: 1, color: financialColors.success}
                            ]),
                            borderRadius: [0, 4, 4, 0]
                        },
                        emphasis: {
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                    {offset: 0, color: financialColors.primary},
                                    {offset: 1, color: financialColors.success}
                                ])
                            }
                        },
                        label: {
                            show: true,
                            position: 'right',
                            formatter: '{c}%',
                            color: financialColors.primary,
                            fontSize: 10,
                            fontWeight: 'bold'
                        }
                    }
                ],
                animationEasing: 'elasticOut',
                animationDelayUpdate: (idx) => idx * 5,
                animationDuration: 3000
            };
            
            myChart.setOption(option);
            
            // 监听窗口大小变化，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 第5张幻灯片：未来趋势预测图表 - 金融风格配色
        function initFutureTrendChart() {
            const chartDom = document.getElementById('futureTrendChart');
            let myChart = null;
            
            // 如果图表已经初始化过，则销毁重建
            if (chartDom) {
                // 尝试获取已存在的图表实例
                const existingChart = echarts.getInstanceByDom(chartDom);
                if (existingChart) {
                    existingChart.dispose();
                }
                myChart = echarts.init(chartDom);
                // 存储图表实例到DOM元素上，以便录制时使用
                chartDom.__myChart = myChart;
            }
            
            // 金融PPT风格配色方案
            const financialColors = {
                primary: '#2c3e50',
                secondary: '#34495e',
                accent: '#3498db',
                highlight: '#e74c3c',
                success: '#27ae60',
                warning: '#f39c12',
                background: '#f8f9fa'
            };
            
            const option = {
                title: {
                    text: '未来三年营收预测',
                    left: 'center',
                    textStyle: {
                        color: financialColors.primary,
                        fontWeight: 'bold',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: financialColors.accent,
                    borderWidth: 1,
                    textStyle: {
                        color: financialColors.primary
                    }
                },
                legend: {
                    data: ['历史数据', '预测数据'],
                    bottom: 0,
                    textStyle: {
                        color: financialColors.secondary
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '10%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['2021', '2022', '2023', '2024E', '2025E', '2026E'],
                    axisLine: {
                        lineStyle: {
                            color: financialColors.secondary
                        }
                    },
                    axisLabel: {
                        color: financialColors.secondary
                    },
                    axisTick: {
                        lineStyle: {
                            color: financialColors.secondary
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '¥{value}亿',
                        color: financialColors.secondary
                    },
                    axisLine: {
                        lineStyle: {
                            color: financialColors.secondary
                        }
                    },
                    axisTick: {
                        lineStyle: {
                            color: financialColors.secondary
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(52, 73, 94, 0.1)'
                        }
                    }
                },
                series: [
                    {
                        name: '历史数据',
                        type: 'line',
                        data: [105.2, 118.3, 128.5, null, null, null],
                        animationDelay: (idx) => idx * 100,
                        lineStyle: {
                            width: 4,
                            color: financialColors.accent
                        },
                        symbol: 'circle',
                        symbolSize: 10,
                        itemStyle: {
                            color: financialColors.accent,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        emphasis: {
                            symbolSize: 12
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '¥{c}亿',
                            color: financialColors.primary,
                            fontSize: 10,
                            fontWeight: 'bold'
                        }
                    },
                    {
                        name: '预测数据',
                        type: 'line',
                        data: [null, null, 128.5, 140.2, 152.8, 167.5],
                        smooth: true,
                        animationDelay: (idx) => idx * 100 + 300,
                        lineStyle: {
                            width: 4,
                            type: 'dashed',
                            color: financialColors.warning
                        },
                        symbol: 'circle',
                        symbolSize: 10,
                        itemStyle: {
                            color: financialColors.warning,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        emphasis: {
                            symbolSize: 12
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: 'rgba(243, 156, 18, 0.3)'},
                                {offset: 1, color: 'rgba(243, 156, 18, 0.05)'}
                            ])
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '¥{c}亿',
                            color: financialColors.primary,
                            fontSize: 10,
                            fontWeight: 'bold'
                        }
                    }
                ],
                animationEasing: 'elasticOut',
                animationDelayUpdate: (idx) => idx * 5,
                animationDuration: 3000
            };
            
            myChart.setOption(option);
            
            // 监听窗口大小变化，重新调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
            // 事件监听器：点击导航按钮切换幻灯片
            navBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const index = parseInt(btn.dataset.index);
                    goToSlide(index);
                });
            });
            
            // 事件监听器：点击左右箭头切换幻灯片
            prevBtn.addEventListener('click', () => {
                // 点击时停止自动播放
                stopAutoplay();
                // 计算新的幻灯片索引
                const newIndex = (currentSlide - 1 + slides.length) % slides.length;
                goToSlide(newIndex);
            });
            
            nextBtn.addEventListener('click', () => {
                // 点击时停止自动播放
                stopAutoplay();
                // 计算新的幻灯片索引
                const newIndex = (currentSlide + 1) % slides.length;
                goToSlide(newIndex);
            });
            
            // 事件监听器：自动播放按钮
            playBtn.addEventListener('click', toggleAutoplay);
            
            // 事件监听器：录制按钮
            recordBtn.addEventListener('click', toggleRecording);
            
            // 事件监听器：幻灯片持续时间变化
            slideDuration.addEventListener('change', () => {
                if (isAutoplaying) {
                    stopAutoplay();
                    startAutoplay();
                }
            });
            
            // 键盘导航
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') {
                    // 计算新的幻灯片索引
                    const newIndex = (currentSlide - 1 + slides.length) % slides.length;
                    goToSlide(newIndex);
                } else if (e.key === 'ArrowRight') {
                    // 计算新的幻灯片索引
                    const newIndex = (currentSlide + 1) % slides.length;
                    goToSlide(newIndex);
                } else if (e.key === ' ') {
                    // 空格键切换自动播放状态
                    e.preventDefault();
                    toggleAutoplay();
                } else if (e.key === 'r' || e.key === 'R') {
                    // R键切换录制状态
                    e.preventDefault();
                    toggleRecording();
                }
            });
            
            // 自动播放功能
            function toggleAutoplay() {
                if (isAutoplaying) {
                    stopAutoplay();
                } else {
                    startAutoplay();
                }
            }
            
            function startAutoplay() {
                isAutoplaying = true;
                playBtn.textContent = '暂停自动播放';
                playBtn.classList.remove('play-btn');
                playBtn.classList.add('pause-btn');

                const duration = parseInt(slideDuration.value);
                console.log('开始自动播放，当前幻灯片:', currentSlide, '间隔时间:', duration + 'ms');

                autoplayInterval = setInterval(() => {
                    // 计算新的幻灯片索引
                    const newIndex = (currentSlide + 1) % slides.length;
                    console.log('自动播放切换：从第', currentSlide, '张到第', newIndex, '张');

                    // 如果是录制状态且即将回到第一张幻灯片（表示播放了一轮），则停止录制
                    if (isRecording && newIndex === 0) {
                        console.log('录制完成一轮，准备停止录制');
                        // 先清除定时器，避免继续播放
                        clearInterval(autoplayInterval);
                        autoplayInterval = null;

                        // 延迟停止录制，确保最后一张幻灯片有足够的显示时间
                        setTimeout(() => {
                            stopRecording();
                        }, 1000);

                        return;
                    }

                    goToSlide(newIndex);
                }, duration);
            }
            
            function stopAutoplay() {
                isAutoplaying = false;
                playBtn.textContent = '开始自动播放';
                playBtn.classList.remove('pause-btn');
                playBtn.classList.add('play-btn');
                
                if (autoplayInterval) {
                    clearInterval(autoplayInterval);
                    autoplayInterval = null;
                }
            }
        
            // 视频录制功能 - 使用屏幕录制API
            async function toggleRecording() {
                if (isRecording) {
                    stopRecording();
                } else {
                    await startRecording();
                }
            }

            // 备用Canvas录制方案
            async function startCanvasRecording() {
                try {
                    const slideshowContainer = document.querySelector('.slideshow-container');

                    // 设置canvas尺寸
                    recordCanvas.width = slideshowContainer.offsetWidth;
                    recordCanvas.height = slideshowContainer.offsetHeight;
                    recordCanvas.style.width = slideshowContainer.offsetWidth + 'px';
                    recordCanvas.style.height = slideshowContainer.offsetHeight + 'px';

                    const ctx = recordCanvas.getContext('2d');
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // 简单的绘制函数
                    function drawSimpleFrame() {
                        try {
                            // 清空canvas
                            ctx.clearRect(0, 0, recordCanvas.width, recordCanvas.height);

                            // 绘制背景
                            ctx.fillStyle = '#f8f9fa';
                            ctx.fillRect(0, 0, recordCanvas.width, recordCanvas.height);

                            // 绘制当前幻灯片信息
                            ctx.font = 'bold 48px Arial, sans-serif';
                            ctx.fillStyle = '#2c3e50';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            ctx.fillText(`幻灯片 ${currentSlide + 1}`, recordCanvas.width / 2, recordCanvas.height / 2);

                            // 绘制时间戳
                            ctx.font = '24px Arial, sans-serif';
                            ctx.fillStyle = '#7f8c8d';
                            const timestamp = new Date().toLocaleTimeString();
                            ctx.fillText(timestamp, recordCanvas.width / 2, recordCanvas.height / 2 + 60);

                        } catch (error) {
                            console.error('绘制错误:', error);
                        }
                    }

                    // 创建视频流
                    const stream = recordCanvas.captureStream(30);

                    // 检测编码格式
                    let mimeType = 'video/webm;codecs=vp9';
                    if (!MediaRecorder.isTypeSupported(mimeType)) {
                        mimeType = 'video/webm;codecs=vp8';
                        if (!MediaRecorder.isTypeSupported(mimeType)) {
                            mimeType = 'video/webm';
                        }
                    }

                    const options = {
                        mimeType: mimeType,
                        videoBitsPerSecond: 3000000
                    };

                    recordRTC = new MediaRecorder(stream, options);
                    const recordedChunks = [];

                    recordRTC.ondataavailable = function(event) {
                        if (event.data.size > 0) {
                            recordedChunks.push(event.data);
                            console.log('Canvas录制数据:', event.data.size, 'bytes');
                        }
                    };

                    recordRTC.onstop = function() {
                        const blob = new Blob(recordedChunks, { type: mimeType });
                        console.log('Canvas录制完成，文件大小:', blob.size, 'bytes');

                        if (blob.size > 0) {
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = 'canvas_recording_' + new Date().toISOString().slice(0,19).replace(/:/g,'-') + '.webm';
                            document.body.appendChild(a);
                            a.click();

                            setTimeout(() => {
                                document.body.removeChild(a);
                                URL.revokeObjectURL(url);
                            }, 100);
                        } else {
                            alert('Canvas录制失败');
                        }
                    };

                    // 开始录制
                    recordRTC.start(1000);

                    // 开始绘制循环
                    window.canvasDrawInterval = setInterval(drawSimpleFrame, 33); // 30fps
                    drawSimpleFrame(); // 立即绘制一帧

                    console.log('Canvas录制已开始');
                    return true;

                } catch (error) {
                    console.error('Canvas录制启动失败:', error);
                    return false;
                }
            }

            async function startRecording() {
                try {
                    console.log('开始Canvas录制...');

                    const slideshowContainer = document.querySelector('.slideshow-container');

                    // 设置canvas尺寸
                    recordCanvas.width = slideshowContainer.offsetWidth;
                    recordCanvas.height = slideshowContainer.offsetHeight;
                    recordCanvas.style.width = slideshowContainer.offsetWidth + 'px';
                    recordCanvas.style.height = slideshowContainer.offsetHeight + 'px';

                    const ctx = recordCanvas.getContext('2d');
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // 检测支持的编码格式
                    let mimeType = 'video/webm;codecs=vp9';
                    if (!MediaRecorder.isTypeSupported(mimeType)) {
                        mimeType = 'video/webm;codecs=vp8';
                        if (!MediaRecorder.isTypeSupported(mimeType)) {
                            mimeType = 'video/webm';
                        }
                    }

                    console.log('使用编码格式:', mimeType);

                    // 创建高质量的绘制函数
                    function drawSlideToCanvas() {
                        try {
                            // 清空canvas
                            ctx.clearRect(0, 0, recordCanvas.width, recordCanvas.height);

                            // 绘制背景渐变
                            const gradient = ctx.createLinearGradient(0, 0, 0, recordCanvas.height);
                            gradient.addColorStop(0, '#f8f9fa');
                            gradient.addColorStop(1, '#e9ecef');
                            ctx.fillStyle = gradient;
                            ctx.fillRect(0, 0, recordCanvas.width, recordCanvas.height);

                            // 获取当前活动幻灯片
                            const activeSlide = document.querySelector('.slide.active');
                            if (!activeSlide) {
                                // 如果没有活动幻灯片，绘制提示信息
                                ctx.font = 'bold 32px Arial, sans-serif';
                                ctx.fillStyle = '#2c3e50';
                                ctx.textAlign = 'center';
                                ctx.textBaseline = 'middle';
                                ctx.fillText('正在加载幻灯片...', recordCanvas.width / 2, recordCanvas.height / 2);
                                return;
                            }

                            // 绘制标题
                            const title = activeSlide.querySelector('.slide-title');
                            if (title) {
                                ctx.font = 'bold 36px "Microsoft YaHei", Arial, sans-serif';
                                ctx.fillStyle = '#2c3e50';
                                ctx.textAlign = 'center';
                                ctx.textBaseline = 'top';

                                // 绘制标题文字
                                const titleText = title.textContent;
                                ctx.fillText(titleText, recordCanvas.width / 2, 50);

                                // 绘制标题下划线
                                ctx.strokeStyle = '#3498db';
                                ctx.lineWidth = 4;
                                ctx.beginPath();
                                const lineWidth = Math.min(titleText.length * 20, recordCanvas.width * 0.6);
                                ctx.moveTo((recordCanvas.width - lineWidth) / 2, 110);
                                ctx.lineTo((recordCanvas.width + lineWidth) / 2, 110);
                                ctx.stroke();
                            }

                            // 绘制数据卡片
                            const dataCards = activeSlide.querySelector('.data-cards');
                            if (dataCards) {
                                const cards = dataCards.querySelectorAll('.data-card');
                                if (cards && cards.length > 0) {
                                    const cardWidth = Math.min(200, (recordCanvas.width * 0.8) / cards.length);
                                    const cardHeight = 100;
                                    const cardsTop = 150;
                                    const totalCardsWidth = cardWidth * cards.length;
                                    const startX = (recordCanvas.width - totalCardsWidth) / 2;

                                    cards.forEach((card, index) => {
                                        const cardX = startX + (index * cardWidth) + (index * 20);

                                        // 绘制卡片背景
                                        ctx.fillStyle = '#ffffff';
                                        ctx.fillRect(cardX, cardsTop, cardWidth - 10, cardHeight);

                                        // 绘制卡片边框
                                        ctx.strokeStyle = '#e9ecef';
                                        ctx.lineWidth = 2;
                                        ctx.strokeRect(cardX, cardsTop, cardWidth - 10, cardHeight);

                                        // 绘制卡片阴影
                                        ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
                                        ctx.shadowBlur = 5;
                                        ctx.shadowOffsetX = 2;
                                        ctx.shadowOffsetY = 2;
                                        ctx.fillRect(cardX, cardsTop, cardWidth - 10, cardHeight);

                                        // 清除阴影
                                        ctx.shadowColor = 'transparent';
                                        ctx.shadowBlur = 0;
                                        ctx.shadowOffsetX = 0;
                                        ctx.shadowOffsetY = 0;

                                        // 绘制卡片内容
                                        const dataValue = card.querySelector('.data-value');
                                        const dataLabel = card.querySelector('.data-label');

                                        if (dataValue) {
                                            ctx.font = 'bold 20px "Microsoft YaHei", Arial, sans-serif';
                                            ctx.fillStyle = '#2c3e50';
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'middle';
                                            ctx.fillText(dataValue.textContent, cardX + (cardWidth - 10) / 2, cardsTop + 35);
                                        }

                                        if (dataLabel) {
                                            ctx.font = '12px "Microsoft YaHei", Arial, sans-serif';
                                            ctx.fillStyle = '#7f8c8d';
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'middle';
                                            ctx.fillText(dataLabel.textContent, cardX + (cardWidth - 10) / 2, cardsTop + 70);
                                        }
                                    });
                                }
                            }

                            // 绘制图表占位区域
                            const chartContainer = activeSlide.querySelector('.chart-container');
                            if (chartContainer) {
                                const chartX = recordCanvas.width * 0.1;
                                const chartY = 300;
                                const chartWidth = recordCanvas.width * 0.8;
                                const chartHeight = recordCanvas.height - chartY - 50;

                                // 检查是否是封面幻灯片
                                const isCoverSlide = activeSlide === slides[0];

                                if (!isCoverSlide) {
                                    // 非封面幻灯片，绘制图表背景
                                    ctx.fillStyle = '#ffffff';
                                    ctx.fillRect(chartX, chartY, chartWidth, chartHeight);

                                    // 绘制图表边框
                                    ctx.strokeStyle = '#e9ecef';
                                    ctx.lineWidth = 2;
                                    ctx.strokeRect(chartX, chartY, chartWidth, chartHeight);

                                    // 尝试获取图表内容
                                    const chartElement = chartContainer.querySelector('div[id$="Chart"]');
                                    if (chartElement && chartElement.__myChart) {
                                        try {
                                            // 尝试获取ECharts的canvas元素
                                            const chartCanvas = chartElement.querySelector('canvas');
                                            if (chartCanvas) {
                                                ctx.drawImage(chartCanvas, chartX + 10, chartY + 10, chartWidth - 20, chartHeight - 20);
                                            } else {
                                                // 如果没有canvas，绘制图表标题
                                                ctx.font = 'bold 24px "Microsoft YaHei", Arial, sans-serif';
                                                ctx.fillStyle = '#2c3e50';
                                                ctx.textAlign = 'center';
                                                ctx.textBaseline = 'middle';
                                                ctx.fillText('财务数据图表', chartX + chartWidth / 2, chartY + chartHeight / 2);
                                            }
                                        } catch (e) {
                                            console.warn('图表绘制失败:', e);
                                            // 绘制占位文本
                                            ctx.font = '18px Arial, sans-serif';
                                            ctx.fillStyle = '#7f8c8d';
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'middle';
                                            ctx.fillText('图表数据', chartX + chartWidth / 2, chartY + chartHeight / 2);
                                        }
                                    }
                                } else {
                                    // 封面幻灯片，绘制装饰性内容
                                    ctx.fillStyle = '#f0f2f5';
                                    ctx.fillRect(chartX, chartY, chartWidth, chartHeight);

                                    // 绘制装饰性图标
                                    ctx.fillStyle = '#3498db';
                                    ctx.beginPath();
                                    ctx.arc(chartX + chartWidth / 2, chartY + chartHeight / 2 - 20, 30, 0, 2 * Math.PI);
                                    ctx.fill();
                                    
                                    ctx.fillStyle = '#ffffff';
                                    ctx.font = 'bold 36px Arial, sans-serif';
                                    ctx.textAlign = 'center';
                                    ctx.textBaseline = 'middle';
                                    ctx.fillText('📊', chartX + chartWidth / 2, chartY + chartHeight / 2 - 20);
                                }
                            }

                            // 绘制页脚信息
                            ctx.font = '14px "Microsoft YaHei", Arial, sans-serif';
                            ctx.fillStyle = '#7f8c8d';
                            ctx.textAlign = 'left';
                            ctx.textBaseline = 'bottom';
                            ctx.fillText(`幻灯片 ${currentSlide + 1} / ${slides.length}`, 20, recordCanvas.height - 20);

                            ctx.textAlign = 'right';
                            const timestamp = new Date().toLocaleString();
                            ctx.fillText(timestamp, recordCanvas.width - 20, recordCanvas.height - 20);

                        } catch (error) {
                            console.error('绘制幻灯片时出错:', error);
                            // 绘制错误信息
                            ctx.fillStyle = '#e74c3c';
                            ctx.font = '24px Arial, sans-serif';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            ctx.fillText('绘制错误', recordCanvas.width / 2, recordCanvas.height / 2);
                        }
                    }

                    // 创建视频流
                    const stream = recordCanvas.captureStream(30);

                    // 创建MediaRecorder实例
                    const options = {
                        mimeType: mimeType,
                        videoBitsPerSecond: 3000000 // 3Mbps
                    };

                    recordRTC = new MediaRecorder(stream, options);
                    const recordedChunks = [];

                    // 监听数据可用事件
                    recordRTC.ondataavailable = function(event) {
                        console.log('数据可用:', event.data.size, 'bytes');
                        if (event.data.size > 0) {
                            recordedChunks.push(event.data);
                        }
                    };

                    // 监听录制停止事件
                    recordRTC.onstop = function() {
                        console.log('录制停止，总数据块:', recordedChunks.length);

                        if (recordedChunks.length === 0) {
                            alert('录制失败：没有捕获到视频数据');
                            return;
                        }

                        // 合并录制的视频数据
                        const blob = new Blob(recordedChunks, { type: mimeType });
                        console.log('生成的blob大小:', blob.size, 'bytes');

                        if (blob.size === 0) {
                            alert('录制失败：生成的视频文件为空');
                            return;
                        }

                        // 创建下载链接
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        const ext = mimeType.includes('mp4') ? 'mp4' : 'webm';
                        a.download = '上市公司财报分析_' + new Date().toISOString().slice(0,19).replace(/:/g,'-') + '.' + ext;
                        document.body.appendChild(a);
                        a.click();

                        // 清理
                        setTimeout(() => {
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                            recordRTC = null;
                        }, 100);
                    };

                    // 监听错误事件
                    recordRTC.onerror = function(event) {
                        console.error('录制错误:', event);
                        alert('录制过程中发生错误');
                    };

                    // 开始录制
                    recordRTC.start(1000); // 每1秒收集一次数据

                    // 开始绘制循环
                    window.canvasDrawInterval = setInterval(drawSlideToCanvas, 33); // 30fps

                    // 立即绘制当前幻灯片，确保第一帧被正确录制
                    console.log('开始录制第一帧，当前幻灯片索引:', currentSlide);
                    drawSlideToCanvas(); // 立即绘制一帧

                    // 短暂延迟后再绘制几帧，确保录制稳定
                    setTimeout(() => drawSlideToCanvas(), 100);
                    setTimeout(() => drawSlideToCanvas(), 200);

                    isRecording = true;
                    recordBtn.textContent = '停止录制并下载';
                    recordBtn.classList.add('recording');

                    // 延迟启动自动播放，确保第一张幻灯片有足够的录制时间
                    if (!isAutoplaying) {
                        const duration = parseInt(slideDuration.value);
                        setTimeout(() => {
                            if (isRecording) { // 确保录制仍在进行
                                startAutoplay();
                            }
                        }, duration); // 等待一个完整的幻灯片显示时间后再开始自动播放
                    }

                    console.log('Canvas录制已开始');

                } catch (error) {
                    console.error('录制启动失败:', error);
                    alert('录制启动失败：' + error.message);
                }
            }

            function stopRecording() {
                if (!recordRTC) return;

                console.log('停止录制...');

                // 停止录制
                isRecording = false;
                recordRTC.stop();

                // 清理Canvas绘制间隔
                if (window.canvasDrawInterval) {
                    clearInterval(window.canvasDrawInterval);
                    window.canvasDrawInterval = null;
                }

                recordBtn.textContent = '开始录制视频';
                recordBtn.classList.remove('recording');

                // 停止自动播放
                stopAutoplay();
            }
        
            // 初始化第一张幻灯片的图表
            initOverviewChart();
            
            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                // 重新初始化当前幻灯片的图表以适应新的窗口大小
                initChartForSlide(currentSlide);
            });
        });
    </script>
</body>
</html>